import { literal } from 'sequelize';

import models from '../models';
import { CashPoolType, CompanyType, GetCashPoolReturnType } from '../types';
const {
  CashPool,
  Cash_Pool_Participants,
  CashPoolParticipantAccounts,
  ParticipantAccountTrails,
  TopCurrencyAccounts,
  CashPoolFile,
  Company,
  ParticipantAccountIds,
  CashPoolBatch,
} = models;

function getCashPool({ where }: any): Promise<(GetCashPoolReturnType & { batches: typeof CashPoolBatch }) | null> {
  return CashPool.findOne({
    where,
    include: [
      { model: Company, as: 'leader' },
      {
        model: TopCurrencyAccounts,
        as: 'topCurrencyAccounts',
        include: {
          model: CashPoolParticipantAccounts,
          as: 'accounts',
          attributes: {
            include: [[literal('"topCurrencyAccounts->accounts->participant"."companyId"'), 'companyId']],
          },
          include: [
            {
              model: Cash_Pool_Participants,
              as: 'participant',
              include: {
                model: Company,
                as: 'company',
              },
            },
            {
              model: ParticipantAccountIds,
              as: 'externalIds',
              attributes: ['cashPoolAccountId', 'externalId'],
            },
          ],
        },
      },
      {
        model: Cash_Pool_Participants,
        as: 'participants',
        include: [
          { model: Company, as: 'company' },
          { model: CashPoolParticipantAccounts, as: 'accounts' },
        ],
      },
      { model: CashPoolFile, as: 'files' },
      { model: CashPoolBatch, as: 'batches', order: [['startDate', 'DESC']], limit: 1 },
    ],
  });
}

function getCashPoolById(id: number, clientId: number): Promise<CashPoolType> {
  return CashPool.findOne({ where: { id, clientId } });
}

function getCashPools({ where }: any): Promise<Array<CashPoolType & { leader: CompanyType }>> {
  return CashPool.findAll({
    where,
    order: [['name', 'ASC']],
    include: { model: Company, as: 'leader' },
  });
}

function getCashPoolCount(clientId: number): Promise<number> {
  return CashPool.count({ where: { clientId } });
}

async function getCashPoolParticipantTrails({ whereCashPool, whereParticipant, whereCompany }: any) {
  const { participants } = await CashPool.findOne({
    where: whereCashPool,
    order: [
      [
        { model: Cash_Pool_Participants, as: 'participants' },
        { model: CashPoolParticipantAccounts, as: 'accounts' },
        { model: ParticipantAccountTrails, as: 'accountTrails' },
        'createdAt',
        'ASC',
      ],
    ],
    include: [
      {
        model: Cash_Pool_Participants,
        as: 'participants',
        attributes: ['id', 'isLeader'],
        where: whereParticipant,
        include: [
          {
            model: CashPoolParticipantAccounts,
            as: 'accounts',
            include: {
              model: ParticipantAccountTrails,
              as: 'accountTrails',
              attributes: ['id', 'balance', 'createdAt'],
            },
          },
          { model: Company, as: 'company', attributes: ['id', 'name'], where: whereCompany },
        ],
      },
    ],
  });

  return participants;
}

function getAllCashPoolParticipants({ cashPoolId }: { cashPoolId: number }) {
  return CashPool.findOne({
    where: { id: cashPoolId },
    attributes: ['id'],
    include: {
      model: Cash_Pool_Participants,
      as: 'participants',
      attributes: {
        include: [
          [literal('"participants->company"."id"'), 'companyId'],
          [literal('"participants->company"."name"'), 'companyName'],
          'uniqueId',
        ],
        exclude: ['id', 'cashPoolId', 'createdAt', 'updatedAt'],
      },
      include: [
        {
          model: Company,
          as: 'company',
          attributes: ['id'],
        },
        {
          model: CashPoolParticipantAccounts,
          as: 'accounts',
          attributes: ['id'],
          include: { model: TopCurrencyAccounts, as: 'topCurrencyAccount', attributes: ['id'] },
        },
      ],
    },
    order: [[{ model: Cash_Pool_Participants, as: 'participants' }, { model: Company, as: 'company' }, 'name', 'ASC']],
  });
}

function createCashPool(cashPool: CashPoolType): Promise<CashPoolType> {
  return CashPool.create(cashPool);
}

function createCashPoolParticipants(participants: any) {
  return Cash_Pool_Participants.bulkCreate(participants);
}

function updateCashPool({ where, attributesToUpdate, returning = true }: any) {
  return CashPool.update(attributesToUpdate, {
    where,
    returning,
  });
}

function deleteCashPool(id: number, clientId: number): Promise<number> {
  return CashPool.destroy({ where: { id, clientId } });
}

export = {
  getCashPool,
  getCashPoolById,
  getCashPools,
  getCashPoolCount,
  getAllCashPoolParticipants,
  createCashPool,
  createCashPoolParticipants,
  updateCashPool,
  deleteCashPool,
  getCashPoolParticipantTrails,
};
